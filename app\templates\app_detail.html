{% extends "base.html" %}

{% block title %}{{ app.name }} - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('main.index', category=app.category) }}">{{ app.category }}</a></li>
            <li class="breadcrumb-item active">{{ app.name }}</li>
        </ol>
    </nav>

    <!-- Modern App Header -->
    <div class="app-detail-header">
        <div class="row align-items-center">
            <div class="col-md-3 text-center mb-3 mb-md-0">
                {% if app.icon_path %}
                <img src="{{ url_for('main.uploaded_file', filename=app.icon_path) }}"
                     class="app-detail-icon" alt="{{ app.name }}">
                {% else %}
                <div class="app-detail-icon-placeholder">
                    <i class="bi bi-app"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <h1 class="app-title">{{ app.name }}</h1>
                <p class="app-developer">by {{ app.developer }}</p>
                <p class="app-description-lead">{{ app.short_description }}</p>

                <div class="app-stats-grid">
                    <div class="app-stat-item">
                        <div class="app-stat-label">Category</div>
                        <div class="app-stat-value">{{ app.category }}</div>
                    </div>
                    <div class="app-stat-item">
                        <div class="app-stat-label">Size</div>
                        <div class="app-stat-value">{{ app.file_size | file_size }}</div>
                    </div>
                    <div class="app-stat-item">
                        <div class="app-stat-label">Downloads</div>
                        <div class="app-stat-value">{{ app.downloads }}</div>
                    </div>
                    <div class="app-stat-item">
                        <div class="app-stat-label">Version</div>
                        <div class="app-stat-value">{{ app.version }}</div>
                    </div>
                </div>

                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="price-display">
                            {% if app.price == 0 %}
                                FREE
                            {% else %}
                                ${{ "%.2f"|format(app.price) }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        {% if app | has_file %}
                            {% if app.price == 0 %}
                                <a href="{{ app.id | download_url }}"
                                   class="btn btn-success btn-lg download-button w-100"
                                   {% if app | is_external %}target="_blank"{% endif %}>
                                    <i class="bi bi-download me-2"></i>
                                    {% if app | is_external %}
                                        Download (External)
                                    {% else %}
                                        Download Now
                                    {% endif %}
                                </a>
                            {% else %}
                                <a href="{{ app.id | download_url }}"
                                   class="btn btn-warning btn-lg download-button w-100">
                                    <i class="bi bi-credit-card me-2"></i> Buy Now
                                </a>
                            {% endif %}
                        {% else %}
                            <button class="btn btn-secondary btn-lg download-button w-100" disabled>
                                <i class="bi bi-x-circle me-2"></i> Not Available
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- App Info -->
        <div class="col-lg-8">

            <!-- Ratings & Reviews - Redesigned -->
            <div class="ratings-reviews-card">
                <div class="ratings-header">
                    <div class="ratings-header-content">
                        <i class="bi bi-star-fill ratings-icon"></i>
                        <h3 class="ratings-title">Ratings & Reviews</h3>
                    </div>
                    <div class="ratings-badge">
                        {% if rating_count > 0 %}
                            {{ rating_count }} Review{{ 's' if rating_count != 1 else '' }}
                        {% else %}
                            No Reviews
                        {% endif %}
                    </div>
                </div>
                <div class="ratings-body">
                    <!-- Rating Summary -->
                    <div class="rating-summary-grid">
                        <div class="rating-score-section">
                            <div class="rating-score-display">
                                <div class="rating-number">
                                    {% if avg_rating > 0 %}
                                        {{ "%.1f"|format(avg_rating) }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </div>
                                <div class="rating-stars-large">
                                    {% for i in range(1, 6) %}
                                        {% if i <= avg_rating %}
                                            <i class="bi bi-star-fill star-filled"></i>
                                        {% elif i - 0.5 <= avg_rating %}
                                            <i class="bi bi-star-half star-half"></i>
                                        {% else %}
                                            <i class="bi bi-star star-empty"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="rating-subtitle">
                                    {% if rating_count > 0 %}
                                        Based on {{ rating_count }} rating{{ 's' if rating_count != 1 else '' }}
                                    {% else %}
                                        No ratings yet
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="rating-distribution-section">
                            <div class="rating-distribution-title">Rating Breakdown</div>
                            {% for star in range(5, 0, -1) %}
                            <div class="rating-bar-item">
                                <span class="rating-bar-label">{{ star }} <i class="bi bi-star-fill"></i></span>
                                <div class="rating-bar-container">
                                    {% set star_count = rating_distribution.get(star, 0) %}
                                    {% set percentage = (star_count / rating_count * 100) if rating_count > 0 else 0 %}
                                    <div class="rating-bar-fill" style="width: {{ percentage }}%"></div>
                                </div>
                                <span class="rating-bar-count">{{ star_count }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Rate This App - Redesigned -->
                    <div class="rating-form-section">
                        {% if user_has_rated %}
                            {% if can_edit_rating %}
                                <div class="rating-form-header">
                                    <i class="bi bi-pencil-square text-warning"></i>
                                    <h5 class="rating-form-title">Edit Your Rating</h5>
                                </div>
                                <div class="rating-alert rating-alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <span>You can edit your rating
                                    {% if edit_time_remaining %}
                                        for {{ edit_time_remaining.hours }}h {{ edit_time_remaining.minutes }}m more
                                    {% endif %}</span>
                                </div>
                                <form method="POST" action="{{ url_for('main.rate_app', app_id=app.id) }}" id="editRatingForm" class="rating-form">
                                    <input type="hidden" name="is_edit" value="true">
                                    <div class="rating-form-group">
                                        <label class="rating-form-label">Your Rating:</label>
                                        <div class="rating-input-modern">
                                            {% for i in range(5, 0, -1) %}
                                            <input type="radio" name="rating" value="{{ i }}" id="edit_star{{ i }}" required>
                                            <label for="edit_star{{ i }}" class="star-label-modern">
                                                <i class="bi bi-star"></i>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="rating-form-group">
                                        <label for="edit_review" class="rating-form-label">Review (Optional):</label>
                                        <textarea class="rating-textarea" id="edit_review" name="review" rows="3"
                                                placeholder="Share your experience with this app..."></textarea>
                                    </div>
                                    <div class="rating-form-group">
                                        <label for="edit_reason" class="rating-form-label">Reason for Edit (Optional):</label>
                                        <input type="text" class="rating-input-text" id="edit_reason" name="edit_reason"
                                               placeholder="Why are you updating your rating?">
                                    </div>
                                    <button type="submit" class="rating-submit-btn rating-submit-warning">
                                        <i class="bi bi-pencil"></i> Update Rating
                                    </button>
                                </form>
                            {% else %}
                                <div class="rating-form-header">
                                    <i class="bi bi-check-circle text-success"></i>
                                    <h5 class="rating-form-title">Your Rating</h5>
                                </div>
                                <div class="rating-alert rating-alert-success">
                                    <i class="bi bi-check-circle"></i>
                                    <span>Thank you for rating this app! You've already submitted your review.</span>
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="rating-form-header">
                                <i class="bi bi-star text-primary"></i>
                                <h5 class="rating-form-title">Rate This App</h5>
                            </div>
                            <form method="POST" action="{{ url_for('main.rate_app', app_id=app.id) }}" id="newRatingForm" class="rating-form">
                                <div class="rating-form-group">
                                    <label class="rating-form-label">Your Rating:</label>
                                    <div class="rating-input-modern">
                                        {% for i in range(5, 0, -1) %}
                                        <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                                        <label for="star{{ i }}" class="star-label-modern">
                                            <i class="bi bi-star"></i>
                                        </label>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="rating-form-group">
                                    <label for="review" class="rating-form-label">Review (Optional):</label>
                                    <textarea class="rating-textarea" id="review" name="review" rows="3"
                                            placeholder="Share your thoughts about this app..."></textarea>
                                </div>
                                <button type="submit" class="rating-submit-btn rating-submit-primary">
                                    <i class="bi bi-star"></i> Submit Rating
                                </button>
                            </form>
                        {% endif %}
                    </div>

                    <!-- Report & Feedback Section -->
                    <div class="rating-feedback-section">
                        <div class="rating-feedback-header">
                            <i class="bi bi-shield-check text-info"></i>
                            <h5 class="rating-feedback-title">Report & Feedback</h5>
                        </div>
                        <div class="rating-feedback-buttons">
                            <button type="button" class="rating-feedback-btn rating-feedback-danger" data-bs-toggle="modal" data-bs-target="#reportModal">
                                <i class="bi bi-flag"></i> Report Abuse
                            </button>
                            <a href="{{ url_for('main.suggestions') }}" class="rating-feedback-btn rating-feedback-info">
                                <i class="bi bi-lightbulb"></i> Submit Suggestion
                            </a>
                        </div>
                    </div>

                    <!-- User Reviews Section -->
                    {% if app.ratings %}
                    <div class="user-reviews-section">
                        <div class="user-reviews-header">
                            <i class="bi bi-chat-square-text text-primary"></i>
                            <h5 class="user-reviews-title">User Reviews</h5>
                        </div>
                        <div class="user-reviews-list">
                            {% for rating in app.ratings[:5] %}
                            <div class="user-review-item">
                                <div class="user-review-header">
                                    <div class="user-review-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= rating.rating %}
                                                <i class="bi bi-star-fill"></i>
                                            {% else %}
                                                <i class="bi bi-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <div class="user-review-meta">
                                        <span class="user-review-author">{{ rating.user.username if rating.user else 'Anonymous' }}</span>
                                        <span class="user-review-date">{{ rating.timestamp.strftime('%B %d, %Y') }}</span>
                                    </div>
                                </div>
                                {% if rating.review %}
                                <div class="user-review-text">{{ rating.review }}</div>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% if app.ratings|length > 5 %}
                            <div class="user-reviews-more">
                                <i class="bi bi-three-dots"></i>
                                <span>and {{ app.ratings|length - 5 }} more reviews</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Description - Redesigned -->
            <div class="description-card">
                <div class="description-header">
                    <div class="description-header-content">
                        <i class="bi bi-file-text description-icon"></i>
                        <h3 class="description-title">Description</h3>
                    </div>
                    <div class="description-actions">
                        <button class="description-expand-btn" id="expandDescBtn" aria-label="Expand description">
                            <i class="bi bi-arrows-expand"></i>
                            <span class="expand-text">Expand</span>
                        </button>
                    </div>
                </div>
                <div class="description-body">
                    <div class="description-content" id="descriptionContent">
                        <div class="description-text">
                            {{ app.description | markdown }}
                        </div>

                        <!-- Key Features Section -->
                        <div class="key-features-section">
                            <div class="key-features-header">
                                <i class="bi bi-star-fill"></i>
                                <h4>Key Features</h4>
                            </div>
                            <div class="key-features-grid">
                                <div class="feature-item">
                                    <i class="bi bi-check-circle-fill feature-icon"></i>
                                    <span>Easy to Use Interface</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-shield-check-fill feature-icon"></i>
                                    <span>Secure & Safe</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-lightning-charge-fill feature-icon"></i>
                                    <span>High Performance</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-arrow-clockwise feature-icon"></i>
                                    <span>Regular Updates</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-headset feature-icon"></i>
                                    <span>24/7 Support</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-cloud-download-fill feature-icon"></i>
                                    <span>Fast Download</span>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Details -->
                        <div class="technical-details-section">
                            <div class="technical-details-header">
                                <i class="bi bi-gear-fill"></i>
                                <h4>Technical Details</h4>
                            </div>
                            <div class="technical-details-grid">
                                <div class="tech-detail-item">
                                    <span class="tech-label">Category:</span>
                                    <span class="tech-value">{{ app.category }}</span>
                                </div>
                                <div class="tech-detail-item">
                                    <span class="tech-label">Developer:</span>
                                    <span class="tech-value">{{ app.developer }}</span>
                                </div>
                                <div class="tech-detail-item">
                                    <span class="tech-label">File Size:</span>
                                    <span class="tech-value">{{ app.file_size or 'N/A' }}</span>
                                </div>
                                <div class="tech-detail-item">
                                    <span class="tech-label">Version:</span>
                                    <span class="tech-value">{{ app.version or 'Latest' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Screenshots -->
            {% if app.screenshots %}
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Screenshots</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for screenshot in app.screenshots %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <img src="{{ url_for('main.uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid rounded screenshot-thumb"
                                 alt="Screenshot {{ loop.index }}"
                                 data-bs-toggle="modal"
                                 data-bs-target="#screenshotModal{{ loop.index }}">
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Screenshot Modals -->
            {% for screenshot in app.screenshots %}
            <div class="modal fade" id="screenshotModal{{ loop.index }}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Screenshot {{ loop.index }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="{{ url_for('main.uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid" alt="Screenshot {{ loop.index }}">
                            {% if screenshot.caption %}
                            <p class="mt-2">{{ screenshot.caption }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- App Information - Redesigned -->
            <div class="app-info-card">
                <div class="app-info-header">
                    <i class="bi bi-info-circle-fill app-info-icon"></i>
                    <h5 class="app-info-title">App Information</h5>
                </div>
                <div class="app-info-body">
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-calendar-plus text-success"></i>
                            <span>Released</span>
                        </div>
                        <div class="app-info-value">{{ app.created_at | datetime('%B %d, %Y') }}</div>
                    </div>
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-arrow-clockwise text-info"></i>
                            <span>Last Updated</span>
                        </div>
                        <div class="app-info-value">{{ app.updated_at | datetime('%B %d, %Y') }}</div>
                    </div>
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-tag text-warning"></i>
                            <span>Category</span>
                        </div>
                        <div class="app-info-value">
                            <span class="category-badge">{{ app.category }}</span>
                        </div>
                    </div>
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-person-badge text-primary"></i>
                            <span>Developer</span>
                        </div>
                        <div class="app-info-value developer-name">{{ app.developer }}</div>
                    </div>
                    {% if app.is_featured %}
                    <div class="app-info-item featured-item">
                        <div class="featured-badge">
                            <i class="bi bi-star-fill"></i>
                            <span>Featured App</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Related Apps -->
            {% if related_apps %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Related Apps</h5>
                </div>
                <div class="card-body">
                    {% for related_app in related_apps %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if related_app.icon_path %}
                            <img src="{{ url_for('main.uploaded_file', filename=related_app.icon_path) }}"
                                 class="related-app-icon" alt="{{ related_app.name }}">
                            {% else %}
                            <div class="related-app-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('main.app_detail', app_id=related_app.id) }}" class="text-decoration-none">
                                    {{ related_app.name }}
                                </a>
                            </h6>
                            <p class="mb-1 small text-muted">{{ related_app.developer }}</p>
                            <small class="text-muted">
                                {% if related_app.price == 0 %}Free{% else %}&dollar;{{ "%.2f"|format(related_app.price) }}{% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Abuse Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Abuse</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm">
                    <div class="mb-3">
                        <label for="reportType" class="form-label">Report Type:</label>
                        <select class="form-select" id="reportType" required>
                            <option value="">Select a reason...</option>
                            <option value="malware">Malware/Virus</option>
                            <option value="inappropriate">Inappropriate Content</option>
                            <option value="copyright">Copyright Violation</option>
                            <option value="spam">Spam/Fake App</option>
                            <option value="misleading">Misleading Information</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="reportReason" class="form-label">Specific Reason:</label>
                        <input type="text" class="form-control" id="reportReason" required
                               placeholder="Brief description of the issue">
                    </div>
                    <div class="mb-3">
                        <label for="reportDescription" class="form-label">Detailed Description (Optional):</label>
                        <textarea class="form-control" id="reportDescription" rows="3"
                                  placeholder="Provide additional details about the issue..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-shield-check"></i>
                        Your report will be encrypted and reviewed by our security team.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="submitReport()">Submit Report</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
/* Apple Liquid Glass App Details Layout */
.app-detail-header {
    background: var(--glass-bg-card);
    backdrop-filter: var(--glass-blur-heavy);
    -webkit-backdrop-filter: var(--glass-blur-heavy);
    border: 1px solid var(--glass-border-dark);
    border-radius: 32px;
    padding: 3rem;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow-hover), var(--edge-glow);
    /* Hardware acceleration */
    transform: translate3d(0, 0, 0);
    will-change: transform, backdrop-filter;
}

.app-detail-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
    opacity: 0.8;
}

.app-detail-icon {
    width: 128px;
    height: 128px;
    object-fit: cover;
    border-radius: 20px;
    border: 3px solid var(--glass-border-dark);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.app-detail-icon:hover {
    transform: scale(1.05);
    border-color: var(--pepe-green);
    box-shadow: 0 12px 40px rgba(107, 191, 75, 0.2);
}

.app-detail-icon-placeholder {
    width: 128px;
    height: 128px;
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
    border: 3px dashed var(--glass-border-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--glass-border);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Beautiful Typography with Apple Liquid Glass */
.app-title {
    font-size: 3rem;
    font-weight: 800;
    font-family: "SF Pro Display", -apple-system, sans-serif;
    background: linear-gradient(135deg, #6ABF4B 0%, #8FD96F 50%, #7FD957 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    text-shadow: 0 0 30px rgba(107, 191, 75, 0.3);
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.app-developer {
    color: #8FD96F;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 15px rgba(143, 217, 111, 0.4);
    letter-spacing: 0.01em;
}

.app-description-lead {
    font-size: 1.3rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 2.5rem;
    font-weight: 400;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.005em;
}

.app-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.app-stat-item {
    background: rgba(107, 191, 75, 0.1);
    border: 1px solid var(--glass-border-dark);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.app-stat-item:hover {
    background: rgba(107, 191, 75, 0.2);
    border-color: var(--pepe-green);
    transform: translateY(-2px);
}

/* Beautiful Stat Display */
.app-stat-label {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.app-stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    background: linear-gradient(135deg, #6ABF4B, #8FD96F);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 15px rgba(107, 191, 75, 0.4);
    letter-spacing: -0.01em;
}

/* Stunning Price Display */
.price-display {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #6ABF4B 0%, #7FD957 50%, #8FD96F 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(107, 191, 75, 0.5);
    letter-spacing: -0.02em;
    font-family: "SF Pro Display", -apple-system, sans-serif;
}

.download-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.download-button:hover::before {
    left: 100%;
}

.screenshot-thumb {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    border: 2px solid var(--glass-border-dark);
    overflow: hidden;
}

.screenshot-thumb:hover {
    transform: scale(1.05);
    border-color: var(--pepe-green);
    box-shadow: 0 8px 32px rgba(107, 191, 75, 0.2);
    /* Fix text blur on scale */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

.related-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid var(--glass-border-dark);
    transition: all 0.3s ease;
}

.related-app-icon:hover {
    border-color: var(--pepe-green);
    transform: scale(1.1);
}

.related-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
    border: 1px dashed var(--glass-border-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--glass-border);
    border-radius: 8px;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
    .app-detail-header {
        padding: 2rem;
        margin-bottom: 2rem;
        border-radius: 24px;
    }

    .app-title {
        font-size: 2.2rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .app-developer {
        text-align: center;
        font-size: 1.1rem;
        margin-bottom: 1.25rem;
    }

    .app-description-lead {
        font-size: 1.1rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .app-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .app-stat-item {
        padding: 1rem;
    }

    .app-stat-label {
        font-size: 0.85rem;
    }

    .app-stat-value {
        font-size: 1.2rem;
    }

    .price-display {
        font-size: 2rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .download-button {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        border-radius: 18px;
    }
}

@media (max-width: 576px) {
    .app-detail-header {
        padding: 1.5rem;
        border-radius: 20px;
    }

    .app-title {
        font-size: 1.9rem;
        text-align: center;
        line-height: 1.2;
    }

    .app-developer {
        text-align: center;
        font-size: 1rem;
    }

    .app-description-lead {
        font-size: 1rem;
        text-align: center;
        line-height: 1.6;
    }

    .app-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .app-stat-item {
        padding: 0.875rem;
    }

    .app-detail-icon {
        width: 100px;
        height: 100px;
        border-radius: 18px;
    }

    .app-detail-icon-placeholder {
        width: 100px;
        height: 100px;
        font-size: 2.75rem;
        border-radius: 18px;
    }

    .price-display {
        font-size: 1.8rem;
    }

    .download-button {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
        border-radius: 16px;
    }
}

/* Rating System Styles */
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input .star-label {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
    margin-right: 0.25rem;
}

.rating-input .star-label:hover,
.rating-input .star-label:hover ~ .star-label,
.rating-input input[type="radio"]:checked ~ .star-label {
    color: #ffc107;
}

.rating-input .star-label:hover {
    transform: scale(1.1);
}

.rating-stars {
    display: inline-block;
}

.rating-stars i {
    margin-right: 0.1rem;
}

/* Redesigned App Information Card */
.app-info-card {
    background: var(--glass-bg-card);
    backdrop-filter: var(--glass-blur-heavy);
    -webkit-backdrop-filter: var(--glass-blur-heavy);
    border: 1px solid var(--glass-border-dark);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--glass-shadow-hover), var(--edge-glow);
    transition: all 0.3s ease;
    position: relative;
}

.app-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #6ABF4B, #8FD96F, #7FD957);
    opacity: 0.8;
}

.app-info-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(107, 191, 75, 0.2),
        var(--edge-glow);
}

.app-info-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 1px solid var(--glass-border-dark);
    background: rgba(107, 191, 75, 0.05);
}

.app-info-icon {
    font-size: 1.25rem;
    color: var(--pepe-green);
    text-shadow: 0 0 10px rgba(107, 191, 75, 0.4);
}

.app-info-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    font-family: "SF Pro Display", -apple-system, sans-serif;
    letter-spacing: 0.01em;
}

.app-info-body {
    padding: 1.5rem;
}

.app-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(107, 191, 75, 0.1);
    transition: all 0.3s ease;
}

.app-info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.app-info-item:hover {
    background: rgba(107, 191, 75, 0.05);
    margin: 0 -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border-radius: 12px;
}

.app-info-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
}

.app-info-label i {
    font-size: 1rem;
}

.app-info-value {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    text-align: right;
    font-size: 0.9rem;
}

.category-badge {
    background: linear-gradient(135deg, #6ABF4B, #8FD96F);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 8px rgba(107, 191, 75, 0.3);
}

.developer-name {
    background: linear-gradient(135deg, #6ABF4B, #8FD96F);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(107, 191, 75, 0.3);
}

.featured-item {
    border-bottom: none !important;
    padding-top: 1.5rem;
    justify-content: center;
}

.featured-badge {
    background: linear-gradient(135deg, #ffc107, #ffb300);
    color: #000;
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow:
        0 4px 16px rgba(255, 193, 7, 0.4),
        0 0 20px rgba(255, 193, 7, 0.2);
    animation: featured-glow 2s ease-in-out infinite alternate;
}

@keyframes featured-glow {
    0% {
        box-shadow:
            0 4px 16px rgba(255, 193, 7, 0.4),
            0 0 20px rgba(255, 193, 7, 0.2);
    }
    100% {
        box-shadow:
            0 6px 20px rgba(255, 193, 7, 0.6),
            0 0 30px rgba(255, 193, 7, 0.4);
    }
}

/* Mobile optimizations for app info */
@media (max-width: 768px) {
    .app-info-card {
        border-radius: 20px;
        margin-bottom: 1.5rem;
    }

    .app-info-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .app-info-body {
        padding: 1.25rem;
    }

    .app-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.75rem 0;
    }

    .app-info-value {
        text-align: left;
        font-size: 0.85rem;
    }

    .app-info-label {
        font-size: 0.9rem;
    }
}

/* Redesigned Ratings & Reviews Section */
.ratings-reviews-card {
    background: var(--glass-bg-card);
    backdrop-filter: var(--glass-blur-heavy);
    -webkit-backdrop-filter: var(--glass-blur-heavy);
    border: 1px solid var(--glass-border-dark);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--glass-shadow-hover), var(--edge-glow);
    margin-top: 1.5rem;
    position: relative;
}

.ratings-reviews-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
    opacity: 0.8;
}

.ratings-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--glass-border-dark);
    background: rgba(255, 107, 107, 0.05);
}

.ratings-header-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ratings-icon {
    font-size: 1.25rem;
    color: #feca57;
    text-shadow: 0 0 10px rgba(254, 202, 87, 0.4);
}

.ratings-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    font-family: "SF Pro Display", -apple-system, sans-serif;
}

.ratings-badge {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.ratings-body {
    padding: 1.5rem;
}

.rating-summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.rating-score-section {
    text-align: center;
}

.rating-score-display {
    background: rgba(254, 202, 87, 0.1);
    border: 1px solid rgba(254, 202, 87, 0.3);
    border-radius: 20px;
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.rating-score-display:hover {
    background: rgba(254, 202, 87, 0.15);
    transform: translateY(-2px);
}

.rating-number {
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    font-family: "SF Pro Display", -apple-system, sans-serif;
}

.rating-stars-large {
    margin-bottom: 1rem;
}

.rating-stars-large i {
    font-size: 1.5rem;
    margin: 0 0.1rem;
}

.star-filled {
    color: #feca57;
    text-shadow: 0 0 8px rgba(254, 202, 87, 0.6);
}

.star-half {
    color: #feca57;
    text-shadow: 0 0 8px rgba(254, 202, 87, 0.6);
}

.star-empty {
    color: rgba(255, 255, 255, 0.3);
}

.rating-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
}

.rating-distribution-section {
    padding: 1rem;
}

.rating-distribution-title {
    font-size: 1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    text-align: center;
}

.rating-bar-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.rating-bar-label {
    font-size: 0.85rem;
    color: #feca57;
    min-width: 2rem;
    font-weight: 600;
}

.rating-bar-container {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.rating-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #feca57, #ff9ff3);
    border-radius: 4px;
    transition: width 0.6s ease;
}

.rating-bar-count {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    min-width: 1.5rem;
    text-align: right;
}

/* Rating Form Section */
.rating-form-section {
    background: rgba(72, 219, 251, 0.05);
    border: 1px solid rgba(72, 219, 251, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.rating-form-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.rating-form-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
}

.rating-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.rating-alert-info {
    background: rgba(72, 219, 251, 0.1);
    border: 1px solid rgba(72, 219, 251, 0.3);
    color: #48dbfb;
}

.rating-alert-success {
    background: rgba(107, 191, 75, 0.1);
    border: 1px solid rgba(107, 191, 75, 0.3);
    color: #6ABF4B;
}

.rating-form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.rating-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.rating-form-label {
    font-size: 0.95rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.rating-input-modern {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 0.25rem;
}

.rating-input-modern input[type="radio"] {
    display: none;
}

.star-label-modern {
    font-size: 1.75rem;
    color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.2s ease;
}

.star-label-modern:hover,
.star-label-modern:hover ~ .star-label-modern,
.rating-input-modern input[type="radio"]:checked ~ .star-label-modern {
    color: #feca57;
    text-shadow: 0 0 8px rgba(254, 202, 87, 0.6);
    transform: scale(1.1);
}

.rating-textarea,
.rating-input-text {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    resize: vertical;
}

.rating-textarea:focus,
.rating-input-text:focus {
    outline: none;
    border-color: #48dbfb;
    box-shadow: 0 0 0 3px rgba(72, 219, 251, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.rating-textarea::placeholder,
.rating-input-text::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.rating-submit-btn {
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    align-self: flex-start;
}

.rating-submit-primary {
    background: linear-gradient(135deg, #48dbfb, #0abde3);
    color: white;
    box-shadow: 0 4px 12px rgba(72, 219, 251, 0.3);
}

.rating-submit-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(72, 219, 251, 0.4);
}

.rating-submit-warning {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
    box-shadow: 0 4px 12px rgba(254, 202, 87, 0.3);
}

.rating-submit-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(254, 202, 87, 0.4);
}

/* Rating Feedback Section */
.rating-feedback-section {
    background: rgba(255, 107, 107, 0.05);
    border: 1px solid rgba(255, 107, 107, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.rating-feedback-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.rating-feedback-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
}

.rating-feedback-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.rating-feedback-btn {
    padding: 0.875rem 1rem;
    border: none;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.rating-feedback-danger {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
}

.rating-feedback-danger:hover {
    background: rgba(255, 107, 107, 0.2);
    transform: translateY(-2px);
    color: #ff6b6b;
}

.rating-feedback-info {
    background: rgba(72, 219, 251, 0.1);
    border: 1px solid rgba(72, 219, 251, 0.3);
    color: #48dbfb;
}

.rating-feedback-info:hover {
    background: rgba(72, 219, 251, 0.2);
    transform: translateY(-2px);
    color: #48dbfb;
    text-decoration: none;
}

/* User Reviews Section */
.user-reviews-section {
    background: rgba(255, 159, 243, 0.05);
    border: 1px solid rgba(255, 159, 243, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.user-reviews-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.user-reviews-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
}

.user-reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.user-review-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.25rem;
    transition: all 0.3s ease;
}

.user-review-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-1px);
}

.user-review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.user-review-stars {
    display: flex;
    gap: 0.1rem;
}

.user-review-stars i {
    font-size: 0.9rem;
    color: #feca57;
}

.user-review-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.user-review-author {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ff9ff3;
}

.user-review-date {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.user-review-text {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.95rem;
    line-height: 1.5;
}

.user-reviews-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    font-style: italic;
}

/* Mobile optimizations for ratings */
@media (max-width: 768px) {
    .rating-summary-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .rating-feedback-buttons {
        grid-template-columns: 1fr;
    }

    .user-review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .user-review-meta {
        align-items: flex-start;
    }

    .rating-number {
        font-size: 2.5rem;
    }

    .rating-stars-large i {
        font-size: 1.25rem;
    }
}

/* Redesigned Description Section */
.description-card {
    background: var(--glass-bg-card);
    backdrop-filter: var(--glass-blur-heavy);
    -webkit-backdrop-filter: var(--glass-blur-heavy);
    border: 1px solid var(--glass-border-dark);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--glass-shadow-hover), var(--edge-glow);
    margin-top: 1.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.description-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #54a0ff, #5f27cd, #a55eea, #fd79a8);
    opacity: 0.8;
}

.description-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(84, 160, 255, 0.2),
        var(--edge-glow);
}

.description-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--glass-border-dark);
    background: rgba(84, 160, 255, 0.05);
}

.description-header-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.description-icon {
    font-size: 1.25rem;
    color: #54a0ff;
    text-shadow: 0 0 10px rgba(84, 160, 255, 0.4);
}

.description-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    font-family: "SF Pro Display", -apple-system, sans-serif;
}

.description-actions {
    display: flex;
    gap: 0.5rem;
}

.description-expand-btn {
    background: rgba(84, 160, 255, 0.1);
    border: 1px solid rgba(84, 160, 255, 0.3);
    border-radius: 12px;
    padding: 0.5rem 1rem;
    color: #54a0ff;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.description-expand-btn:hover {
    background: rgba(84, 160, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(84, 160, 255, 0.3);
}

.description-body {
    padding: 1.5rem;
}

.description-content {
    max-height: 400px;
    overflow: hidden;
    transition: max-height 0.5s ease;
    position: relative;
}

.description-content.expanded {
    max-height: none;
    overflow: visible;
}

.description-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.description-text h1, .description-text h2, .description-text h3,
.description-text h4, .description-text h5, .description-text h6 {
    color: #54a0ff !important;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.description-text p {
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.85) !important;
}

.description-text ul, .description-text ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.description-text li {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.85);
}

.description-text code {
    background: rgba(84, 160, 255, 0.1);
    border: 1px solid rgba(84, 160, 255, 0.3);
    border-radius: 4px;
    padding: 0.2rem 0.4rem;
    color: #54a0ff;
    font-family: 'Courier New', monospace;
}

.description-text blockquote {
    border-left: 3px solid #54a0ff;
    padding-left: 1rem;
    margin: 1rem 0;
    background: rgba(84, 160, 255, 0.05);
    border-radius: 0 8px 8px 0;
}



/* Fade effect for collapsed content */
.description-content:not(.expanded)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(transparent, var(--glass-bg-card));
    pointer-events: none;
}

/* Mobile optimizations for description */
@media (max-width: 768px) {
    .description-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.25rem 1.25rem 1rem;
    }

    .description-body {
        padding: 1.25rem;
    }

    .key-features-grid {
        grid-template-columns: 1fr;
    }

    .technical-details-grid {
        grid-template-columns: 1fr;
    }

    .tech-detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .tech-value {
        text-align: left;
    }

    .description-content {
        max-height: 300px;
    }
}

@media (max-width: 576px) {
    .description-card {
        border-radius: 20px;
        margin-top: 1rem;
    }

    .description-header {
        padding: 1rem 1rem 0.75rem;
    }

    .description-body {
        padding: 1rem;
    }


    .description-content {
        max-height: 250px;
    }
}
</style>

<script>
function submitReport() {
    const reportType = document.getElementById('reportType').value;
    const reportReason = document.getElementById('reportReason').value;
    const reportDescription = document.getElementById('reportDescription').value;

    if (!reportType || !reportReason) {
        alert('Please fill in all required fields.');
        return;
    }

    const data = {
        report_type: reportType,
        reason: reportReason,
        description: reportDescription
    };

    fetch(`/app/{{ app.id }}/report`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('reportForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while submitting the report.');
    });
}
</script>
{% endblock %}
