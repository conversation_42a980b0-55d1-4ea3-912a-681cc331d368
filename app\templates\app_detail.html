{% extends "base.html" %}

{% block title %}{{ app.name }} - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('main.index', category=app.category) }}">{{ app.category }}</a></li>
            <li class="breadcrumb-item active">{{ app.name }}</li>
        </ol>
    </nav>

    <!-- Modern App Header -->
    <div class="app-detail-header">
        <div class="row align-items-center">
            <div class="col-md-3 text-center mb-3 mb-md-0">
                {% if app.icon_path %}
                <img src="{{ url_for('main.uploaded_file', filename=app.icon_path) }}"
                     class="app-detail-icon" alt="{{ app.name }}">
                {% else %}
                <div class="app-detail-icon-placeholder">
                    <i class="bi bi-app"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <h1 class="app-title">{{ app.name }}</h1>
                <p class="app-developer">by {{ app.developer }}</p>
                <p class="app-description-lead">{{ app.short_description }}</p>

                <div class="app-stats-grid">
                    <div class="app-stat-item">
                        <div class="app-stat-label">Category</div>
                        <div class="app-stat-value">{{ app.category }}</div>
                    </div>
                    <div class="app-stat-item">
                        <div class="app-stat-label">Size</div>
                        <div class="app-stat-value">{{ app.file_size | file_size }}</div>
                    </div>
                    <div class="app-stat-item">
                        <div class="app-stat-label">Downloads</div>
                        <div class="app-stat-value">{{ app.downloads }}</div>
                    </div>
                    <div class="app-stat-item">
                        <div class="app-stat-label">Version</div>
                        <div class="app-stat-value">{{ app.version }}</div>
                    </div>
                </div>

                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="price-display">
                            {% if app.price == 0 %}
                                FREE
                            {% else %}
                                ${{ "%.2f"|format(app.price) }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        {% if app | has_file %}
                            {% if app.price == 0 %}
                                <a href="{{ app.id | download_url }}"
                                   class="btn btn-success btn-lg download-button w-100"
                                   {% if app | is_external %}target="_blank"{% endif %}>
                                    <i class="bi bi-download me-2"></i>
                                    {% if app | is_external %}
                                        Download (External)
                                    {% else %}
                                        Download Now
                                    {% endif %}
                                </a>
                            {% else %}
                                <a href="{{ app.id | download_url }}"
                                   class="btn btn-warning btn-lg download-button w-100">
                                    <i class="bi bi-credit-card me-2"></i> Buy Now
                                </a>
                            {% endif %}
                        {% else %}
                            <button class="btn btn-secondary btn-lg download-button w-100" disabled>
                                <i class="bi bi-x-circle me-2"></i> Not Available
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- App Info -->
        <div class="col-lg-8">

            <!-- Rating Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Ratings & Reviews</h3>
                </div>
                <div class="card-body">
                    <!-- Rating Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h2 class="display-4 text-primary">
                                    {% if avg_rating > 0 %}
                                        {{ "%.1f"|format(avg_rating) }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </h2>
                                <div class="rating-stars mb-2">
                                    {% for i in range(1, 6) %}
                                        {% if i <= avg_rating %}
                                            <i class="bi bi-star-fill text-warning fs-4"></i>
                                        {% elif i - 0.5 <= avg_rating %}
                                            <i class="bi bi-star-half text-warning fs-4"></i>
                                        {% else %}
                                            <i class="bi bi-star text-muted fs-4"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted">
                                    {% if rating_count > 0 %}
                                        {{ rating_count }} rating{{ 's' if rating_count != 1 else '' }}
                                    {% else %}
                                        No ratings yet
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Rating Distribution -->
                            {% for star in range(5, 0, -1) %}
                            <div class="d-flex align-items-center mb-1">
                                <span class="me-2">{{ star }} <i class="bi bi-star-fill text-warning"></i></span>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    {% set star_count = rating_distribution.get(star, 0) %}
                                    {% set percentage = (star_count / rating_count * 100) if rating_count > 0 else 0 %}
                                    <div class="progress-bar bg-warning" style="width: {{ percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ star_count }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Rate This App -->
                    <div class="border-top pt-4">
                        {% if user_has_rated %}
                            {% if can_edit_rating %}
                                <h5>Edit Your Rating</h5>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    You can edit your rating
                                    {% if edit_time_remaining %}
                                        for {{ edit_time_remaining.hours }}h {{ edit_time_remaining.minutes }}m more
                                    {% endif %}
                                </div>
                                <form method="POST" action="{{ url_for('main.rate_app', app_id=app.id) }}" id="editRatingForm">
                                    <input type="hidden" name="is_edit" value="true">
                                    <div class="mb-3">
                                        <label class="form-label">Your Rating:</label>
                                        <div class="rating-input">
                                            {% for i in range(5, 0, -1) %}
                                            <input type="radio" name="rating" value="{{ i }}" id="edit_star{{ i }}" required>
                                            <label for="edit_star{{ i }}" class="star-label">
                                                <i class="bi bi-star"></i>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_review" class="form-label">Review (Optional):</label>
                                        <textarea class="form-control" id="edit_review" name="review" rows="3"
                                                placeholder="Share your experience with this app..."></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_reason" class="form-label">Reason for Edit (Optional):</label>
                                        <input type="text" class="form-control" id="edit_reason" name="edit_reason"
                                               placeholder="Why are you updating your rating?">
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-pencil"></i> Update Rating
                                    </button>
                                </form>
                            {% else %}
                                <h5>Your Rating</h5>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle"></i>
                                    Thank you for rating this app! You've already submitted your review.
                                </div>
                            {% endif %}
                        {% else %}
                            <h5>Rate This App</h5>
                            <form method="POST" action="{{ url_for('main.rate_app', app_id=app.id) }}" id="newRatingForm">
                                <div class="mb-3">
                                    <label class="form-label">Your Rating:</label>
                                    <div class="rating-input">
                                        {% for i in range(5, 0, -1) %}
                                        <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                                        <label for="star{{ i }}" class="star-label">
                                            <i class="bi bi-star"></i>
                                        </label>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="review" class="form-label">Review (Optional):</label>
                                    <textarea class="form-control" id="review" name="review" rows="3"
                                            placeholder="Share your thoughts about this app..."></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-star"></i> Submit Rating
                                </button>
                            </form>
                        {% endif %}
                    </div>

                    <!-- Report Abuse & Suggestions -->
                    <div class="border-top pt-4">
                        <h5>Report & Feedback</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-danger btn-sm w-100" data-bs-toggle="modal" data-bs-target="#reportModal">
                                    <i class="bi bi-flag"></i> Report Abuse
                                </button>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('main.suggestions') }}" class="btn btn-outline-info btn-sm w-100">
                                    <i class="bi bi-lightbulb"></i> Submit Suggestion
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Existing Reviews -->
                    {% if app.ratings %}
                    <div class="border-top pt-4 mt-4">
                        <h5>User Reviews</h5>
                        {% for rating in app.ratings[:5] %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= rating.rating %}
                                                <i class="bi bi-star-fill text-warning"></i>
                                            {% else %}
                                                <i class="bi bi-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">
                                        by {{ rating.user.username if rating.user else 'Anonymous' }}
                                        on {{ rating.timestamp.strftime('%B %d, %Y') }}
                                    </small>
                                </div>
                            </div>
                            {% if rating.review %}
                            <p class="mt-2 mb-0">{{ rating.review }}</p>
                            {% endif %}
                        </div>
                        {% endfor %}
                        {% if app.ratings|length > 5 %}
                        <small class="text-muted">... and {{ app.ratings|length - 5 }} more reviews</small>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Description -->
            <div class="card mt-4">
              <div class="card-header">
                <h3>Description</h3>
              </div>
              <div class="card-body">
                {{ app.description | markdown }}
              </div>
            </div>


            <!-- Screenshots -->
            {% if app.screenshots %}
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Screenshots</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for screenshot in app.screenshots %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <img src="{{ url_for('main.uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid rounded screenshot-thumb"
                                 alt="Screenshot {{ loop.index }}"
                                 data-bs-toggle="modal"
                                 data-bs-target="#screenshotModal{{ loop.index }}">
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Screenshot Modals -->
            {% for screenshot in app.screenshots %}
            <div class="modal fade" id="screenshotModal{{ loop.index }}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Screenshot {{ loop.index }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="{{ url_for('main.uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid" alt="Screenshot {{ loop.index }}">
                            {% if screenshot.caption %}
                            <p class="mt-2">{{ screenshot.caption }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- App Information - Redesigned -->
            <div class="app-info-card">
                <div class="app-info-header">
                    <i class="bi bi-info-circle-fill app-info-icon"></i>
                    <h5 class="app-info-title">App Information</h5>
                </div>
                <div class="app-info-body">
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-calendar-plus text-success"></i>
                            <span>Released</span>
                        </div>
                        <div class="app-info-value">{{ app.created_at | datetime('%B %d, %Y') }}</div>
                    </div>
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-arrow-clockwise text-info"></i>
                            <span>Last Updated</span>
                        </div>
                        <div class="app-info-value">{{ app.updated_at | datetime('%B %d, %Y') }}</div>
                    </div>
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-tag text-warning"></i>
                            <span>Category</span>
                        </div>
                        <div class="app-info-value">
                            <span class="category-badge">{{ app.category }}</span>
                        </div>
                    </div>
                    <div class="app-info-item">
                        <div class="app-info-label">
                            <i class="bi bi-person-badge text-primary"></i>
                            <span>Developer</span>
                        </div>
                        <div class="app-info-value developer-name">{{ app.developer }}</div>
                    </div>
                    {% if app.is_featured %}
                    <div class="app-info-item featured-item">
                        <div class="featured-badge">
                            <i class="bi bi-star-fill"></i>
                            <span>Featured App</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Related Apps -->
            {% if related_apps %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Related Apps</h5>
                </div>
                <div class="card-body">
                    {% for related_app in related_apps %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if related_app.icon_path %}
                            <img src="{{ url_for('main.uploaded_file', filename=related_app.icon_path) }}"
                                 class="related-app-icon" alt="{{ related_app.name }}">
                            {% else %}
                            <div class="related-app-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('main.app_detail', app_id=related_app.id) }}" class="text-decoration-none">
                                    {{ related_app.name }}
                                </a>
                            </h6>
                            <p class="mb-1 small text-muted">{{ related_app.developer }}</p>
                            <small class="text-muted">
                                {% if related_app.price == 0 %}Free{% else %}&dollar;{{ "%.2f"|format(related_app.price) }}{% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Abuse Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Abuse</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm">
                    <div class="mb-3">
                        <label for="reportType" class="form-label">Report Type:</label>
                        <select class="form-select" id="reportType" required>
                            <option value="">Select a reason...</option>
                            <option value="malware">Malware/Virus</option>
                            <option value="inappropriate">Inappropriate Content</option>
                            <option value="copyright">Copyright Violation</option>
                            <option value="spam">Spam/Fake App</option>
                            <option value="misleading">Misleading Information</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="reportReason" class="form-label">Specific Reason:</label>
                        <input type="text" class="form-control" id="reportReason" required
                               placeholder="Brief description of the issue">
                    </div>
                    <div class="mb-3">
                        <label for="reportDescription" class="form-label">Detailed Description (Optional):</label>
                        <textarea class="form-control" id="reportDescription" rows="3"
                                  placeholder="Provide additional details about the issue..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-shield-check"></i>
                        Your report will be encrypted and reviewed by our security team.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="submitReport()">Submit Report</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
/* Apple Liquid Glass App Details Layout */
.app-detail-header {
    background: var(--glass-bg-card);
    backdrop-filter: var(--glass-blur-heavy);
    -webkit-backdrop-filter: var(--glass-blur-heavy);
    border: 1px solid var(--glass-border-dark);
    border-radius: 32px;
    padding: 3rem;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow-hover), var(--edge-glow);
    /* Hardware acceleration */
    transform: translate3d(0, 0, 0);
    will-change: transform, backdrop-filter;
}

.app-detail-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
    opacity: 0.8;
}

.app-detail-icon {
    width: 128px;
    height: 128px;
    object-fit: cover;
    border-radius: 20px;
    border: 3px solid var(--glass-border-dark);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.app-detail-icon:hover {
    transform: scale(1.05);
    border-color: var(--pepe-green);
    box-shadow: 0 12px 40px rgba(107, 191, 75, 0.2);
}

.app-detail-icon-placeholder {
    width: 128px;
    height: 128px;
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
    border: 3px dashed var(--glass-border-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--glass-border);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Beautiful Typography with Apple Liquid Glass */
.app-title {
    font-size: 3rem;
    font-weight: 800;
    font-family: "SF Pro Display", -apple-system, sans-serif;
    background: linear-gradient(135deg, #6ABF4B 0%, #8FD96F 50%, #7FD957 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    text-shadow: 0 0 30px rgba(107, 191, 75, 0.3);
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.app-developer {
    color: #8FD96F;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 15px rgba(143, 217, 111, 0.4);
    letter-spacing: 0.01em;
}

.app-description-lead {
    font-size: 1.3rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 2.5rem;
    font-weight: 400;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.005em;
}

.app-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.app-stat-item {
    background: rgba(107, 191, 75, 0.1);
    border: 1px solid var(--glass-border-dark);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.app-stat-item:hover {
    background: rgba(107, 191, 75, 0.2);
    border-color: var(--pepe-green);
    transform: translateY(-2px);
}

/* Beautiful Stat Display */
.app-stat-label {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.app-stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    background: linear-gradient(135deg, #6ABF4B, #8FD96F);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 15px rgba(107, 191, 75, 0.4);
    letter-spacing: -0.01em;
}

/* Stunning Price Display */
.price-display {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #6ABF4B 0%, #7FD957 50%, #8FD96F 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(107, 191, 75, 0.5);
    letter-spacing: -0.02em;
    font-family: "SF Pro Display", -apple-system, sans-serif;
}

.download-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.download-button:hover::before {
    left: 100%;
}

.screenshot-thumb {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    border: 2px solid var(--glass-border-dark);
    overflow: hidden;
}

.screenshot-thumb:hover {
    transform: scale(1.05);
    border-color: var(--pepe-green);
    box-shadow: 0 8px 32px rgba(107, 191, 75, 0.2);
    /* Fix text blur on scale */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

.related-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid var(--glass-border-dark);
    transition: all 0.3s ease;
}

.related-app-icon:hover {
    border-color: var(--pepe-green);
    transform: scale(1.1);
}

.related-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
    border: 1px dashed var(--glass-border-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--glass-border);
    border-radius: 8px;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
    .app-detail-header {
        padding: 2rem;
        margin-bottom: 2rem;
        border-radius: 24px;
    }

    .app-title {
        font-size: 2.2rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .app-developer {
        text-align: center;
        font-size: 1.1rem;
        margin-bottom: 1.25rem;
    }

    .app-description-lead {
        font-size: 1.1rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .app-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .app-stat-item {
        padding: 1rem;
    }

    .app-stat-label {
        font-size: 0.85rem;
    }

    .app-stat-value {
        font-size: 1.2rem;
    }

    .price-display {
        font-size: 2rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .download-button {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        border-radius: 18px;
    }
}

@media (max-width: 576px) {
    .app-detail-header {
        padding: 1.5rem;
        border-radius: 20px;
    }

    .app-title {
        font-size: 1.9rem;
        text-align: center;
        line-height: 1.2;
    }

    .app-developer {
        text-align: center;
        font-size: 1rem;
    }

    .app-description-lead {
        font-size: 1rem;
        text-align: center;
        line-height: 1.6;
    }

    .app-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .app-stat-item {
        padding: 0.875rem;
    }

    .app-detail-icon {
        width: 100px;
        height: 100px;
        border-radius: 18px;
    }

    .app-detail-icon-placeholder {
        width: 100px;
        height: 100px;
        font-size: 2.75rem;
        border-radius: 18px;
    }

    .price-display {
        font-size: 1.8rem;
    }

    .download-button {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
        border-radius: 16px;
    }
}

/* Rating System Styles */
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input .star-label {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
    margin-right: 0.25rem;
}

.rating-input .star-label:hover,
.rating-input .star-label:hover ~ .star-label,
.rating-input input[type="radio"]:checked ~ .star-label {
    color: #ffc107;
}

.rating-input .star-label:hover {
    transform: scale(1.1);
}

.rating-stars {
    display: inline-block;
}

.rating-stars i {
    margin-right: 0.1rem;
}

/* Redesigned App Information Card */
.app-info-card {
    background: var(--glass-bg-card);
    backdrop-filter: var(--glass-blur-heavy);
    -webkit-backdrop-filter: var(--glass-blur-heavy);
    border: 1px solid var(--glass-border-dark);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--glass-shadow-hover), var(--edge-glow);
    transition: all 0.3s ease;
    position: relative;
}

.app-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #6ABF4B, #8FD96F, #7FD957);
    opacity: 0.8;
}

.app-info-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(107, 191, 75, 0.2),
        var(--edge-glow);
}

.app-info-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 1px solid var(--glass-border-dark);
    background: rgba(107, 191, 75, 0.05);
}

.app-info-icon {
    font-size: 1.25rem;
    color: var(--pepe-green);
    text-shadow: 0 0 10px rgba(107, 191, 75, 0.4);
}

.app-info-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    font-family: "SF Pro Display", -apple-system, sans-serif;
    letter-spacing: 0.01em;
}

.app-info-body {
    padding: 1.5rem;
}

.app-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(107, 191, 75, 0.1);
    transition: all 0.3s ease;
}

.app-info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.app-info-item:hover {
    background: rgba(107, 191, 75, 0.05);
    margin: 0 -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border-radius: 12px;
}

.app-info-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
}

.app-info-label i {
    font-size: 1rem;
}

.app-info-value {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    text-align: right;
    font-size: 0.9rem;
}

.category-badge {
    background: linear-gradient(135deg, #6ABF4B, #8FD96F);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 8px rgba(107, 191, 75, 0.3);
}

.developer-name {
    background: linear-gradient(135deg, #6ABF4B, #8FD96F);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(107, 191, 75, 0.3);
}

.featured-item {
    border-bottom: none !important;
    padding-top: 1.5rem;
    justify-content: center;
}

.featured-badge {
    background: linear-gradient(135deg, #ffc107, #ffb300);
    color: #000;
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow:
        0 4px 16px rgba(255, 193, 7, 0.4),
        0 0 20px rgba(255, 193, 7, 0.2);
    animation: featured-glow 2s ease-in-out infinite alternate;
}

@keyframes featured-glow {
    0% {
        box-shadow:
            0 4px 16px rgba(255, 193, 7, 0.4),
            0 0 20px rgba(255, 193, 7, 0.2);
    }
    100% {
        box-shadow:
            0 6px 20px rgba(255, 193, 7, 0.6),
            0 0 30px rgba(255, 193, 7, 0.4);
    }
}

/* Mobile optimizations for app info */
@media (max-width: 768px) {
    .app-info-card {
        border-radius: 20px;
        margin-bottom: 1.5rem;
    }

    .app-info-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .app-info-body {
        padding: 1.25rem;
    }

    .app-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.75rem 0;
    }

    .app-info-value {
        text-align: left;
        font-size: 0.85rem;
    }

    .app-info-label {
        font-size: 0.9rem;
    }
}
</style>

<script>
function submitReport() {
    const reportType = document.getElementById('reportType').value;
    const reportReason = document.getElementById('reportReason').value;
    const reportDescription = document.getElementById('reportDescription').value;

    if (!reportType || !reportReason) {
        alert('Please fill in all required fields.');
        return;
    }

    const data = {
        report_type: reportType,
        reason: reportReason,
        description: reportDescription
    };

    fetch(`/app/{{ app.id }}/report`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('reportForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while submitting the report.');
    });
}
</script>
{% endblock %}
