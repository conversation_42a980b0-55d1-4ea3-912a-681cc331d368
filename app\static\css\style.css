:root {
  /* Apple Liquid Glass + Pepe Green Perfection */
  --pepe-green: #6ABF4B;
  --pepe-green-dark: #3DAE2B;
  --pepe-green-light: #8FD96F;
  --pepe-green-accent: #5BAF3C;
  --pepe-moss: #4A8F35;
  --pepe-lime: #7FD957;

  /* Bootstrap Color Overrides with Pepe Green */
  --bs-primary: var(--pepe-green);
  --bs-secondary: #333;
  --bs-success: var(--pepe-green);
  --bs-info: #0cf;
  --bs-warning: #fa0;
  --bs-danger: #f36;
  --bs-light: #1a1a1a;
  --bs-dark: #0a0a0a;
  --bs-body-bg: #0a0a0a;
  --bs-body-color: #fff;
  --bs-card-bg: #1e1e1e;
  --bs-border-color: #333;
  --bs-navbar-bg: #0f0f0f;
  --bs-placeholder-color: #888;

  /* Legacy Pepe Variables (Maintained for Compatibility) */
  --pepe-primary: var(--pepe-green);
  --pepe-primary-dark: var(--pepe-green-dark);
  --pepe-primary-light: var(--pepe-green-light);
  --pepe-accent: #ff6b35;
  --pepe-glow-primary: 0 0 20px #6ABF4B80;
  --pepe-glow-secondary: 0 0 15px #6ABF4B4d;
  --pepe-glow-accent: 0 0 25px #ff6b3599;

  /* Apple Liquid Glass Variables - WWDC 2025 Spec */
  --glass-blur: saturate(180%) blur(12px);
  --glass-blur-heavy: saturate(180%) blur(20px);
  --glass-blur-light: saturate(180%) blur(8px);
  --glass-blur-dynamic: saturate(180%) blur(16px);
  --glass-opacity: 0.4;
  --glass-opacity-heavy: 0.75;
  --glass-opacity-light: 0.25;
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-dark: rgba(107, 191, 75, 0.4);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --glass-shadow-hover: 0 16px 64px rgba(107, 191, 75, 0.15);
  --glass-shadow-depth: 0 4px 16px rgba(0, 0, 0, 0.08);

  /* Apple Liquid Glass Edge Highlights */
  --edge-glow: 0 0 20px rgba(107, 191, 75, 0.3);
  --edge-glow-soft: 0 0 12px rgba(107, 191, 75, 0.2);
  --edge-glow-intense: 0 0 32px rgba(107, 191, 75, 0.5);
  --edge-highlight: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  --edge-highlight-green: inset 0 1px 0 rgba(107, 191, 75, 0.2);

  /* Liquid Glass Background Variants */
  --glass-bg: rgba(255, 255, 255, var(--glass-opacity));
  --glass-bg-dark: rgba(10, 10, 10, var(--glass-opacity-heavy));
  --glass-bg-card: rgba(30, 30, 30, var(--glass-opacity));
  --glass-bg-nav: rgba(15, 15, 15, var(--glass-opacity-heavy));
  --glass-bg-panel: rgba(20, 20, 20, var(--glass-opacity));

  /* Motion & Interaction Variables */
  --motion-duration: 0.3s;
  --motion-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --motion-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Custom Glassy Cursor */
  --cursor-glow: 0 0 16px rgba(107, 191, 75, 0.4);
}
.table {
  --bs-table-bg: var(--bs-card-bg);
  --bs-table-border-color: var(--bs-border-color);
  color: var(--bs-body-color) !important;
}
.text-muted {
  color: #6c757d !important;
}
[data-theme="dark"] .text-muted,
[data-theme="pepe"] .text-muted {
  color: #adb5bd !important;
}
.border {
  border-color: var(--bs-border-color) !important;
}
.recaptcha-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
@media (max-width: 400px) {
  .g-recaptcha {
    transform: scale(0.85);
    transform-origin: center;
  }
}
@media (max-width: 320px) {
  .g-recaptcha {
    transform: scale(0.75);
  }
}
/* Premium Glass Hero Section */
.hero-section {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 24px !important;
  min-height: 200px !important;
  box-shadow: var(--glass-shadow), var(--border-glow-soft) !important;
  position: relative !important;
  overflow: hidden !important;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  opacity: 0.8;
  z-index: 1;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 30% 70%, rgba(107, 191, 75, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(107, 191, 75, 0.05) 0%, transparent 50%);
  animation: hero-float 8s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes hero-float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(-10px, -10px) rotate(1deg); }
  66% { transform: translate(10px, -5px) rotate(-1deg); }
}

[data-theme="dark"] .hero-section {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) !important;
}
/* Premium Glass App Cards */
/* Apple Liquid Glass App Cards */
.app-card {
  background: var(--glass-bg-card) !important;
  backdrop-filter: var(--glass-blur) !important;
  -webkit-backdrop-filter: var(--glass-blur) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 24px !important;
  color: var(--bs-body-color) !important;
  box-shadow: var(--glass-shadow), var(--edge-glow-soft) !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
  position: relative !important;
  overflow: hidden !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, backdrop-filter, box-shadow;
}

.app-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--glass-border-dark), transparent);
  opacity: 0.8;
  z-index: 1;
}

.app-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(107, 191, 75, 0.1), transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

/* Apple Liquid Glass App Card Hover */
.app-card:hover {
  transform: translateY(-12px) scale(1.02) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  box-shadow: var(--glass-shadow-hover), var(--edge-glow) !important;
  border-color: var(--pepe-green) !important;
  /* Preserve text clarity */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.app-card:hover::before {
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  opacity: 1;
  box-shadow: var(--border-glow-soft);
}

.app-card:hover::after {
  left: 100%;
}

[data-theme="dark"] .app-card:hover {
  box-shadow: var(--glass-shadow-hover), var(--border-glow) !important;
}
.card-img-top-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
  border-bottom: 1px solid var(--glass-border-dark);
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  position: relative;
}

.card-img-top-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(107, 191, 75, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.app-card:hover .card-img-top-container::before {
  opacity: 1;
}

[data-theme="dark"] .card-img-top-container {
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  border-bottom-color: var(--glass-border-dark);
}
.app-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.375rem;
  transition: transform 0.3s ease;
  background: #2a2a2a;
  /* Fix image rendering issues */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.app-card:hover .app-icon {
  transform: scale(1.05);
  /* Fix text blur on scale */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}
.app-icon-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
  border: 2px dashed var(--glass-border-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: var(--glass-border);
  border-radius: 0.375rem;
}
[data-theme="dark"] .app-icon-placeholder {
  background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
  border-color: var(--glass-border-dark);
  color: var(--glass-border);
}
.app-detail-icon {
  max-width: 128px;
  max-height: 128px;
  object-fit: cover;
  border-radius: 0.5rem;
}
.app-detail-icon-placeholder {
  width: 128px;
  height: 128px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #6c757d;
  border-radius: 0.5rem;
  margin: 0 auto;
}
[data-theme="dark"] .app-detail-icon-placeholder {
  background: var(--bs-light);
  border-color: var(--bs-border-color);
  color: #adb5bd;
}
.screenshot-thumb {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  border-radius: 0.375rem;
}
.screenshot-thumb:hover {
  transform: scale(1.05);
  /* Fix text blur on scale */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}
.related-app-icon {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 0.375rem;
}
.related-app-icon-placeholder {
  width: 48px;
  height: 48px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #6c757d;
  border-radius: 0.375rem;
}
[data-theme="dark"] .related-app-icon-placeholder {
  background: var(--bs-light);
  border-color: var(--bs-border-color);
  color: #adb5bd;
}
.admin-app-icon {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 0.375rem;
}
.admin-app-icon-placeholder {
  width: 48px;
  height: 48px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #6c757d;
  border-radius: 0.375rem;
}
[data-theme="dark"] .admin-app-icon-placeholder {
  background: var(--bs-light);
  border-color: var(--bs-border-color);
  color: #adb5bd;
}
@media (max-width: 768px) {
  .hero-section {
    text-align: center;
    padding: 2rem 1rem !important;
  }
  .hero-section .display-4 {
    font-size: 2rem;
  }
  .card-img-top-container {
    height: 150px;
  }
  .app-icon-placeholder {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}
@media (max-width: 576px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .hero-section .display-4 {
    font-size: 1.75rem;
  }
  .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.loading {
  opacity: 0.6;
  pointer-events: none;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}
.theme-toggle {
  transition: transform 0.2s ease-in-out;
}
.theme-toggle:hover {
  transform: scale(1.1);
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bs-dark);
}
[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #555;
}
[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #777;
}
@keyframes pepe-bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
@keyframes pepe-pulse-glow {
  0% {
    text-shadow: var(--pepe-glow-secondary);
  }
  100% {
    text-shadow: var(--pepe-glow-primary);
  }
}
@keyframes pepe-scan {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}
@keyframes pepe-float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
@keyframes pepe-pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}
@keyframes pepe-featured-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: var(--pepe-glow-accent);
  }
}
/* Apple Liquid Glass Base Styles - WWDC 2025 */

/* Custom Glassy Cursor with Pepe Green Glow */
* {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="none" stroke="%236ABF4B" stroke-width="1.5" opacity="0.6"/><circle cx="12" cy="12" r="3" fill="%236ABF4B" opacity="0.8"/><circle cx="12" cy="12" r="15" fill="none" stroke="%236ABF4B" stroke-width="0.5" opacity="0.3"/></svg>'), auto;
}

a, button, .btn, [role="button"], input[type="submit"], input[type="button"] {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28"><circle cx="14" cy="14" r="12" fill="none" stroke="%236ABF4B" stroke-width="2" opacity="0.8"/><circle cx="14" cy="14" r="4" fill="%236ABF4B" opacity="1"/><circle cx="14" cy="14" r="18" fill="none" stroke="%236ABF4B" stroke-width="1" opacity="0.4"/></svg>'), pointer !important;
}

body {
  background:
    radial-gradient(circle at 20% 80%, rgba(107, 191, 75, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(107, 191, 75, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(107, 191, 75, 0.04) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%) !important;
  color: var(--bs-body-color) !important;
  font-family: "SF Pro Display", "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif;
  font-weight: 400;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  min-height: 100vh;
  transition: all var(--motion-duration) var(--motion-ease);
  position: relative;
  overflow-x: hidden;
}

/* Apple Liquid Glass Dynamic Lighting Layer */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(107, 191, 75, 0.12) 0%, transparent 40%),
    radial-gradient(circle at 20% 80%, rgba(107, 191, 75, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(107, 191, 75, 0.06) 0%, transparent 60%);
  pointer-events: none;
  z-index: -1;
  transition: background var(--motion-duration) var(--motion-ease);
}

/* Adaptive Lighting on Scroll */
body.scrolled::before {
  background:
    radial-gradient(circle at 50% 20%, rgba(107, 191, 75, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(107, 191, 75, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(107, 191, 75, 0.04) 0%, transparent 60%);
}

/* Apple Liquid Glass Panel System */
.glass-panel {
  background: var(--glass-bg-dark);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border-dark);
  border-radius: 20px;
  box-shadow: var(--glass-shadow), var(--edge-glow-soft);
  position: relative;
  overflow: hidden;
  transition: all var(--motion-duration) var(--motion-ease);
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, box-shadow, backdrop-filter;
}

/* Apple Edge Highlight System */
.glass-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(107, 191, 75, 0.4), transparent);
  opacity: 0.8;
  border-radius: 20px 20px 0 0;
  transition: opacity var(--motion-duration) var(--motion-ease);
}

/* Dynamic Hover with Adaptive Lighting */
.glass-panel:hover {
  transform: translateY(-4px) scale(1.01);
  backdrop-filter: var(--glass-blur-dynamic);
  -webkit-backdrop-filter: var(--glass-blur-dynamic);
  box-shadow: var(--glass-shadow-hover), var(--edge-glow);
  border-color: var(--pepe-green);
}

.glass-panel:hover::before {
  opacity: 1;
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  box-shadow: var(--edge-glow-soft);
}

/* Glass Panel Variants */
.glass-panel-heavy {
  background: var(--glass-bg-dark);
  backdrop-filter: blur(var(--glass-blur-heavy));
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy));
  border: 1px solid var(--glass-border-dark);
}

.glass-panel-light {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(var(--glass-blur-light));
  -webkit-backdrop-filter: blur(var(--glass-blur-light));
  border: 1px solid rgba(255, 255, 255, 0.1);
}
/* Apple Liquid Glass Cards */
.card {
  background: var(--glass-bg-card) !important;
  backdrop-filter: var(--glass-blur) !important;
  -webkit-backdrop-filter: var(--glass-blur) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 24px !important;
  color: var(--bs-body-color) !important;
  box-shadow: var(--glass-shadow), var(--edge-glow-soft) !important;
  position: relative;
  overflow: hidden;
  transition: all var(--motion-duration) var(--motion-ease) !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, backdrop-filter, box-shadow;
}

/* Apple Edge Highlight for Cards */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(107, 191, 75, 0.3), transparent);
  opacity: 0.6;
  z-index: 1;
  border-radius: 24px 24px 0 0;
  transition: all var(--motion-duration) var(--motion-ease);
}

/* Liquid Glass Hover with Motion-Driven Layering */
.card:hover {
  transform: translateY(-8px) scale(1.015) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  box-shadow: var(--glass-shadow-hover), var(--edge-glow) !important;
  border-color: var(--pepe-green) !important;
  /* Preserve text clarity */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.card:hover::before {
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  opacity: 1;
  box-shadow: var(--edge-glow-soft);
}
.form-text {
  color: var(--bs-body-color);
}
/* Apple Liquid Glass Form Controls */
.form-control,
.form-select {
  background: var(--glass-bg-card) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 16px !important;
  color: var(--bs-body-color) !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
  box-shadow: var(--glass-shadow-depth), var(--edge-highlight) !important;
  font-family: "SF Pro Display", "Inter", sans-serif !important;
  font-weight: 500 !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, backdrop-filter, box-shadow;
}

/* Apple Liquid Glass Form Focus States */
.form-control:focus,
.form-select:focus {
  background: rgba(30, 30, 30, 0.6) !important;
  backdrop-filter: var(--glass-blur) !important;
  -webkit-backdrop-filter: var(--glass-blur) !important;
  border-color: var(--pepe-green) !important;
  color: var(--bs-body-color) !important;
  box-shadow: var(--edge-glow), var(--edge-highlight-green) !important;
  transform: translateY(-2px) scale(1.01) !important;
  outline: none !important;
}

.form-control:hover,
.form-select:hover {
  border-color: var(--pepe-green-accent) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--edge-glow-soft), var(--edge-highlight) !important;
}

/* Apple Liquid Glass Category Dropdown Enhancement */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%238FD96F' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 16px center !important;
  background-size: 18px 14px !important;
  padding-right: 48px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
}

.form-select option {
  background: rgba(20, 20, 20, 0.95) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 2px 0 !important;
}

.form-select option:hover,
.form-select option:checked {
  background: rgba(107, 191, 75, 0.2) !important;
  color: #8FD96F !important;
}
input[type="file"].form-control::file-selector-button {
  background-color: var(--bs-secondary) !important;
  color: var(--bs-body-color) !important;
  border: none !important;
  padding: 0.375rem 0.75rem;
  margin-right: 0.5rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}
input[type="file"].form-control::file-selector-button:hover {
  background-color: var(--bs-primary) !important;
  color: var(--bs-white) !important;
}
.form-control::placeholder {
  color: var(--bs-placeholder-color) !important;
}
/* Apple Liquid Glass Navigation */
.navbar {
  background: var(--glass-bg-nav) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  border-bottom: 1px solid var(--glass-border-dark) !important;
  box-shadow: var(--glass-shadow-depth), var(--edge-glow-soft) !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1030 !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: backdrop-filter, box-shadow;
}

.navbar::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  opacity: 0.6;
}

.navbar:hover::before {
  opacity: 1;
  box-shadow: var(--border-glow-soft);
}
/* Apple Liquid Glass Dropdowns - Enhanced */
.dropdown-menu {
  background: rgba(20, 20, 20, 0.85) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 24px !important;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(107, 191, 75, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  padding: 20px !important;
  margin-top: 16px !important;
  min-width: 240px !important;
  max-width: 360px !important;
  z-index: 1055 !important;
  position: absolute !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, opacity, backdrop-filter;
  /* Smooth animations */
  opacity: 0;
  transform: translateY(-15px) scale(0.92);
  transition: all 0.4s var(--motion-bounce);
}

/* Dropdown Show Animation */
.dropdown-menu.show {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Fix dropdown positioning */
.dropdown-menu[data-bs-popper] {
  left: auto !important;
  right: 0 !important;
}

/* Apple Liquid Glass Dropdown Items */
.dropdown-item {
  color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 18px !important;
  margin: 6px 0 !important;
  padding: 16px 20px !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
  position: relative;
  overflow: hidden;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 14px;
  font-family: "SF Pro Display", "Inter", sans-serif;
  letter-spacing: 0.01em;
}

/* Apple-style Shimmer Effect */
.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(107, 191, 75, 0.2), transparent);
  transition: left 0.6s var(--motion-ease);
  z-index: 0;
}

/* Liquid Glass Hover State */
.dropdown-item:hover {
  background: rgba(107, 191, 75, 0.25) !important;
  color: #8FD96F !important;
  transform: translateX(8px) scale(1.02) !important;
  box-shadow:
    var(--edge-glow-soft),
    0 4px 20px rgba(107, 191, 75, 0.2) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  text-shadow: 0 0 10px rgba(143, 217, 111, 0.4);
}

.dropdown-item:hover::before {
  left: 100%;
}

/* Focus state for keyboard navigation */
.dropdown-item:focus {
  background: rgba(107, 191, 75, 0.3) !important;
  color: #8FD96F !important;
  outline: 2px solid var(--pepe-green);
  outline-offset: -2px;
  text-shadow: 0 0 15px rgba(143, 217, 111, 0.5);
}

/* Dropdown icons */
.dropdown-item i {
  color: rgba(107, 191, 75, 0.8);
  font-size: 1.1rem;
  transition: all var(--motion-duration) var(--motion-ease);
}

.dropdown-item:hover i,
.dropdown-item:focus i {
  color: #8FD96F;
  text-shadow: 0 0 8px rgba(143, 217, 111, 0.6);
}
/* Premium Glass Modals */
.modal-content {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 24px !important;
  color: var(--bs-body-color) !important;
  box-shadow: var(--glass-shadow), var(--border-glow-soft) !important;
  overflow: hidden;
  position: relative;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  opacity: 0.8;
  z-index: 1;
}

.modal-header {
  border-bottom: 1px solid var(--glass-border-dark) !important;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(var(--glass-blur-light));
  -webkit-backdrop-filter: blur(var(--glass-blur-light));
}

.modal-footer {
  border-top: 1px solid var(--glass-border-dark) !important;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(var(--glass-blur-light));
  -webkit-backdrop-filter: blur(var(--glass-blur-light));
}

.modal-backdrop {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}
/* Premium Glass Footer */
footer.bg-dark {
  background: var(--glass-bg-nav) !important;
  backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) !important;
  border-top: 1px solid var(--glass-border-dark) !important;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3), var(--border-glow-soft) !important;
  position: relative !important;
}

footer.bg-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--pepe-green), transparent);
  opacity: 0.6;
}
.navbar {
  border-bottom: 2px solid var(--pepe-primary);
  box-shadow: var(--pepe-glow-secondary);
}
.pepe-glow {
  color: var(--pepe-primary);
  text-shadow: var(--pepe-glow-primary);
  animation: pepe-pulse-glow 2s ease-in-out infinite alternate;
}
.pepe-brand {
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none !important;
  color: var(--bs-body-color) !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}
.pepe-brand:hover {
  color: var(--pepe-primary) !important;
  text-shadow: var(--pepe-glow-secondary);
  transform: scale(1.05);
}
.pepe-logo {
  font-size: 2rem;
  animation: pepe-bounce 2s infinite;
  filter: drop-shadow(var(--pepe-glow-secondary));
}
.nav-link.active {
  color: #fff !important;
}
.pepe-tagline {
  font-size: 0.8rem;
  color: var(--pepe-primary);
  margin-left: 0.5rem;
  opacity: 0.8;
  text-shadow: var(--pepe-glow-secondary);
}
.hero-section {
  background: linear-gradient(
    135deg,
    var(--bs-card-bg) 0%,
    var(--bs-light) 50%,
    var(--bs-card-bg) 100%
  );
  border: 2px solid var(--pepe-primary);
  box-shadow: var(--pepe-glow-primary), inset 0 0 50px #00ff881a;
  position: relative;
  overflow: hidden;
}
.hero-section::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, #00ff881a, transparent);
  animation: pepe-scan 4s linear infinite;
  pointer-events: none;
}
.hero-section h1 {
  text-shadow: var(--pepe-glow-primary);
  color: var(--pepe-primary);
}
.hero-section h2 {
  color: var(--pepe-accent);
  text-shadow: var(--pepe-glow-accent);
}
.app-card {
  background: var(--bs-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.app-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #00ff881a, transparent);
  transition: left 0.5s ease;
}
.app-card:hover {
  transform: translateY(-5px);
  border-color: var(--pepe-primary);
  box-shadow: var(--pepe-glow-primary);
}
.app-card:hover::before {
  left: 100%;
}
/* Premium Glass Buttons */
/* Apple Liquid Glass Primary Button */
.btn-primary {
  background: linear-gradient(135deg, var(--pepe-green), var(--pepe-green-dark)) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 20px !important;
  color: #000 !important;
  font-weight: 600 !important;
  font-family: "SF Pro Display", "Inter", sans-serif !important;
  letter-spacing: 0.3px !important;
  padding: 14px 28px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
  box-shadow: var(--glass-shadow), var(--edge-glow-soft) !important;
  text-transform: none !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, box-shadow, backdrop-filter;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  opacity: 0.8;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--pepe-green-light), var(--pepe-green)) !important;
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: var(--glass-shadow-hover), var(--border-glow) !important;
  border-color: var(--pepe-green-light) !important;
  color: #000 !important;
  /* Fix text blur on scale */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.btn-primary:hover::after {
  left: 100%;
}

.btn-primary:active {
  transform: translateY(-1px) scale(0.98) !important;
}

/* Secondary Button Glass Style */
.btn-secondary {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur)) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 16px !important;
  color: var(--bs-body-color) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.btn-secondary:hover {
  background: rgba(107, 191, 75, 0.1) !important;
  border-color: var(--pepe-green) !important;
  color: var(--pepe-green) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--border-glow-soft) !important;
}
[data-theme="pepe"] .pepe-brand {
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none !important;
  color: var(--bs-body-color) !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}
[data-theme="pepe"] .pepe-brand:hover {
  color: var(--pepe-primary) !important;
  text-shadow: var(--pepe-glow-secondary);
  transform: scale(1.05);
}
[data-theme="pepe"] .pepe-logo {
  font-size: 2rem;
  animation: pepe-bounce 2s infinite;
}
[data-theme="pepe"] .pepe-tagline {
  font-size: 0.8rem;
  color: var(--pepe-primary);
  margin-left: 0.5rem;
  opacity: 0.8;
}
.pepe-hero-section {
  background: linear-gradient(
    135deg,
    var(--pepe-bg-secondary) 0%,
    var(--pepe-bg-card) 50%,
    var(--pepe-bg-secondary) 100%
  );
  border: 2px solid var(--pepe-primary);
  box-shadow: var(--pepe-glow-primary), inset 0 0 50px #00ff881a;
  position: relative;
  overflow: hidden;
}
.pepe-hero-section::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, #00ff881a, transparent);
  animation: pepe-scan 4s linear infinite;
  pointer-events: none;
}
.pepe-hero-title {
  text-shadow: var(--pepe-glow-primary);
  margin-bottom: 1rem;
}
.pepe-glow {
  color: var(--pepe-primary);
  text-shadow: var(--pepe-glow-primary);
  animation: pepe-pulse-glow 2s ease-in-out infinite alternate;
}
.pepe-subtitle {
  color: var(--pepe-accent);
  text-shadow: var(--pepe-glow-accent);
  font-weight: 600;
}
.pepe-hero-text {
  color: var(--pepe-text-secondary);
  font-size: 1.1rem;
}
.pepe-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}
.pepe-stat-item {
  color: var(--pepe-primary);
  font-weight: 600;
  font-size: 1rem;
  text-shadow: var(--pepe-glow-secondary);
  animation: pepe-stat-glow 3s ease-in-out infinite alternate;
}
.pepe-hero-icon {
  position: relative;
  display: inline-block;
}
.pepe-mega-logo {
  font-size: 8rem;
  display: block;
  animation: pepe-float 3s ease-in-out infinite;
  filter: drop-shadow(var(--pepe-glow-primary));
}
.pepe-pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid var(--pepe-primary);
  border-radius: 50%;
  animation: pepe-pulse-ring 2s ease-out infinite;
}
.pepe-pulse-ring-2 {
  animation-delay: 1s;
  border-color: var(--pepe-accent);
}
.pepe-app-card {
  background: var(--pepe-bg-card);
  border: 1px solid var(--pepe-border);
  border-radius: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.pepe-app-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #00ff881a, transparent);
  transition: left 0.5s ease;
}
.pepe-app-card:hover {
  transform: translateY(-5px);
  border-color: var(--pepe-primary);
  box-shadow: var(--pepe-glow-primary);
}
.pepe-app-card:hover::before {
  left: 100%;
}
.pepe-featured-card {
  border-color: var(--pepe-accent);
  box-shadow: var(--pepe-glow-accent);
}
.pepe-featured-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(45deg, var(--pepe-accent), var(--pepe-warning));
  color: var(--pepe-bg-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 700;
  z-index: 10;
  animation: pepe-featured-pulse 2s ease-in-out infinite;
}
.pepe-app-title {
  color: var(--pepe-primary);
  font-weight: 600;
  text-shadow: var(--pepe-glow-secondary);
}
.pepe-app-desc {
  color: var(--pepe-text-secondary);
  font-size: 0.9rem;
}
.pepe-price {
  color: var(--pepe-success);
  font-weight: 700;
  text-shadow: var(--pepe-glow-secondary);
}
.pepe-category-badge {
  background: linear-gradient(
    45deg,
    var(--pepe-primary),
    var(--pepe-primary-light)
  );
  color: var(--pepe-bg-primary);
  border: none;
  font-weight: 600;
}
.pepe-icon-placeholder {
  background: linear-gradient(
    45deg,
    var(--pepe-bg-secondary),
    var(--pepe-bg-card)
  );
  border: 2px dashed var(--pepe-primary);
  color: var(--pepe-primary);
}
.pepe-btn-primary {
  background: linear-gradient(
    45deg,
    var(--pepe-primary),
    var(--pepe-primary-light)
  );
  border: none;
  color: var(--pepe-bg-primary);
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.pepe-btn-primary:hover {
  background: linear-gradient(
    45deg,
    var(--pepe-primary-light),
    var(--pepe-primary)
  );
  transform: scale(1.05);
  box-shadow: var(--pepe-glow-primary);
  color: var(--pepe-bg-primary);
}
.pepe-glow-btn {
  position: relative;
  overflow: hidden;
}
.pepe-glow-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #ffffff4d, transparent);
  transition: left 0.5s ease;
}
.pepe-glow-btn:hover::before {
  left: 100%;
}
.pepe-footer {
  background: linear-gradient(
    135deg,
    var(--pepe-bg-nav) 0%,
    var(--pepe-bg-secondary) 100%
  );
  border-top: 2px solid var(--pepe-primary);
  box-shadow: 0 -5px 20px #00ff884d;
}
.pepe-footer-title {
  color: var(--pepe-primary);
  text-shadow: var(--pepe-glow-secondary);
}
.pepe-footer-text {
  color: var(--pepe-text-secondary);
}
.pepe-footer-small {
  color: var(--pepe-text-muted);
  font-size: 0.8rem;
  opacity: 0.7;
}
.pepe-dropdown {
  background: var(--pepe-bg-card);
  border: 1px solid var(--pepe-primary);
  box-shadow: var(--pepe-glow-secondary);
}
.pepe-dropdown .dropdown-item {
  color: var(--pepe-text-primary);
  transition: all 0.3s ease;
}
.pepe-dropdown .dropdown-item:hover {
  background: var(--pepe-primary);
  color: var(--pepe-bg-primary);
}
.pepe-admin-link {
  color: var(--pepe-primary) !important;
  text-shadow: var(--pepe-glow-secondary);
}
@keyframes pepe-bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
@keyframes pepe-pulse-glow {
  0% {
    text-shadow: var(--pepe-glow-secondary);
  }
  100% {
    text-shadow: var(--pepe-glow-primary);
  }
}
@keyframes pepe-float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
@keyframes pepe-pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}
@keyframes pepe-scan {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}
@keyframes pepe-stat-glow {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
    text-shadow: var(--pepe-glow-primary);
  }
}
@keyframes pepe-featured-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: var(--pepe-glow-accent);
  }
}
@media (max-width: 768px) {
  .pepe-mega-logo {
    font-size: 4rem;
  }
  .pepe-stats {
    gap: 1rem;
    justify-content: center;
  }
  .pepe-stat-item {
    font-size: 0.9rem;
  }
  .pepe-tagline {
    display: none;
  }
}
/* Premium Glass Enhancements */
.badge {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur-light)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-light)) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 12px !important;
  color: var(--pepe-green) !important;
  font-weight: 600 !important;
  padding: 6px 12px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.badge:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: var(--border-glow-soft) !important;
  border-color: var(--pepe-green) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, var(--pepe-green), var(--pepe-green-dark)) !important;
  color: #000 !important;
  border-color: var(--pepe-green-light) !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, #ffa500, #ff8c00) !important;
  color: #000 !important;
  border-color: #ffb84d !important;
}

.badge.bg-info {
  background: linear-gradient(135deg, #00bcd4, #0097a7) !important;
  color: #000 !important;
  border-color: #26c6da !important;
}

/* Glass Alert Styles */
.alert {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur)) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 16px !important;
  color: var(--bs-body-color) !important;
  box-shadow: var(--glass-shadow) !important;
}

.alert-success {
  border-color: var(--pepe-green) !important;
  background: rgba(107, 191, 75, 0.1) !important;
  color: var(--pepe-green) !important;
}

.alert-danger {
  border-color: #f36 !important;
  background: rgba(255, 51, 102, 0.1) !important;
  color: #ff6b9d !important;
}

.alert-warning {
  border-color: #ffa500 !important;
  background: rgba(255, 165, 0, 0.1) !important;
  color: #ffb84d !important;
}

/* Glass Table Styles */
.table {
  background: var(--glass-bg-card) !important;
  backdrop-filter: blur(var(--glass-blur-light)) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-light)) !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: var(--glass-shadow) !important;
}

.table th {
  background: rgba(107, 191, 75, 0.1) !important;
  border-color: var(--glass-border-dark) !important;
  color: var(--pepe-green) !important;
  font-weight: 600 !important;
}

.table td {
  border-color: var(--glass-border-dark) !important;
  color: var(--bs-body-color) !important;
}

.table tbody tr:hover {
  background: rgba(107, 191, 75, 0.05) !important;
}

@media (max-width: 576px) {
  .pepe-hero-title {
    font-size: 2rem;
  }
  .pepe-subtitle {
    font-size: 1.2rem;
  }
  .pepe-stats {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  /* Mobile Glass Optimizations */
  .glass-panel,
  .card,
  .app-card {
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    margin-bottom: 1rem !important;
  }

  .hero-section {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    padding: 2rem 1rem !important;
    margin-bottom: 2rem !important;
  }

  /* Better mobile card layout */
  .app-card {
    transform: none !important;
    transition: box-shadow 0.3s ease !important;
  }

  .app-card:hover {
    transform: none !important;
    box-shadow: var(--glass-shadow-hover) !important;
  }

  /* Mobile dropdown improvements */
  .dropdown-menu {
    position: fixed !important;
    top: auto !important;
    left: 1rem !important;
    right: 1rem !important;
    width: auto !important;
    min-width: auto !important;
    margin-top: 0.5rem !important;
  }

  /* Mobile navbar improvements */
  .navbar-collapse {
    background: rgba(30, 30, 30, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-radius: 12px !important;
    margin-top: 0.5rem !important;
    padding: 1rem !important;
    border: 1px solid var(--glass-border-dark) !important;
  }
}

/* Beautiful Typography & Text Colors */
h1, h2, h3, h4, h5, h6 {
  font-family: "SF Pro Display", -apple-system, sans-serif !important;
  font-weight: 700 !important;
  letter-spacing: -0.01em !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

h1 {
  background: linear-gradient(135deg, #6ABF4B 0%, #8FD96F 50%, #7FD957 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(107, 191, 75, 0.3) !important;
}

h2 {
  color: #8FD96F !important;
  text-shadow: 0 0 20px rgba(143, 217, 111, 0.3) !important;
}

h3 {
  color: #7FD957 !important;
  text-shadow: 0 0 15px rgba(127, 217, 87, 0.3) !important;
}

h4, h5, h6 {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

.card-title {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
  letter-spacing: 0.005em !important;
}

.card-text {
  color: rgba(255, 255, 255, 0.85) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  line-height: 1.6 !important;
}

/* Beautiful link colors */
a {
  color: #8FD96F !important;
  text-decoration: none !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
}

a:hover {
  color: #6ABF4B !important;
  text-shadow: 0 0 10px rgba(107, 191, 75, 0.4) !important;
}

/* Beautiful badge colors */
.badge {
  background: linear-gradient(135deg, rgba(107, 191, 75, 0.2), rgba(143, 217, 111, 0.3)) !important;
  color: #8FD96F !important;
  border: 1px solid rgba(107, 191, 75, 0.4) !important;
  text-shadow: 0 0 8px rgba(143, 217, 111, 0.3) !important;
  font-weight: 600 !important;
}

.text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Premium Link Styles */
a {
  color: var(--pepe-green) !important;
  text-decoration: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

a:hover {
  color: var(--pepe-green-light) !important;
  text-shadow: var(--border-glow-soft) !important;
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--pepe-green);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a:hover::after {
  width: 100%;
}

/* Premium Scrollbar Glass Style */
::-webkit-scrollbar {
  width: 12px;
  background: var(--glass-bg-card);
  backdrop-filter: blur(var(--glass-blur-light));
  -webkit-backdrop-filter: blur(var(--glass-blur-light));
}

::-webkit-scrollbar-track {
  background: var(--glass-bg-card);
  border-radius: 10px;
  border: 1px solid var(--glass-border-dark);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--pepe-green), var(--pepe-green-dark));
  border-radius: 10px;
  border: 1px solid var(--glass-border-dark);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--pepe-green-light), var(--pepe-green));
  box-shadow: var(--border-glow-soft);
}

/* Performance Optimizations */
.glass-panel,
.card,
.app-card,
.navbar,
.modal-content,
.dropdown-menu,
.btn-primary,
.btn-secondary,
.form-control,
.form-select {
  will-change: auto !important;
  transform: translateZ(0) !important;
  contain: layout style paint !important;
}

/* Optimize animations for better performance */
@media (prefers-reduced-motion: no-preference) {
  .glass-panel,
  .card,
  .app-card {
    will-change: transform, opacity !important;
  }

  .glass-panel:hover,
  .card:hover,
  .app-card:hover {
    will-change: transform, opacity, box-shadow !important;
  }
}

/* GPU acceleration for smooth animations */
.app-card,
.card,
.btn-primary,
.dropdown-menu {
  transform: translate3d(0, 0, 0) !important;
  -webkit-transform: translate3d(0, 0, 0) !important;
}

/* Apple Liquid Glass Dark Mode - WWDC 2025 */
[data-theme="dark"] .glass-panel,
[data-theme="dark"] .card,
[data-theme="dark"] .app-card {
  background: rgba(10, 10, 10, 0.6) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  border-color: rgba(107, 191, 75, 0.3) !important;
  box-shadow: var(--glass-shadow), var(--edge-glow-soft) !important;
}

[data-theme="dark"] .dropdown-menu {
  background: rgba(5, 5, 5, 0.8) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  border-color: rgba(107, 191, 75, 0.4) !important;
}

[data-theme="dark"] .navbar {
  background: rgba(8, 8, 8, 0.7) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
}

[data-theme="dark"] .text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
}

[data-theme="dark"] body {
  background:
    radial-gradient(circle at 20% 80%, rgba(107, 191, 75, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(107, 191, 75, 0.04) 0%, transparent 50%),
    linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%) !important;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-panel,
  .card,
  .app-card {
    border-width: 2px !important;
    border-color: var(--pepe-green) !important;
  }

  .text-muted {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

/* Apple Liquid Glass Modal System */
.modal-backdrop {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
}

.modal-content {
  background: var(--glass-bg-card) !important;
  backdrop-filter: var(--glass-blur-heavy) !important;
  -webkit-backdrop-filter: var(--glass-blur-heavy) !important;
  border: 1px solid var(--glass-border-dark) !important;
  border-radius: 28px !important;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.5),
    0 0 50px rgba(107, 191, 75, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  /* Hardware acceleration */
  transform: translate3d(0, 0, 0);
  will-change: transform, backdrop-filter;
}

.modal-header {
  border-bottom: 1px solid var(--glass-border-dark) !important;
  background: linear-gradient(90deg, transparent, rgba(107, 191, 75, 0.1), transparent);
  border-radius: 28px 28px 0 0 !important;
}

.modal-title {
  color: #8FD96F !important;
  font-weight: 700 !important;
  text-shadow: 0 0 15px rgba(143, 217, 111, 0.3) !important;
}

.modal-body {
  color: rgba(255, 255, 255, 0.9) !important;
}

.btn-close {
  background: rgba(107, 191, 75, 0.2) !important;
  border-radius: 50% !important;
  opacity: 0.8 !important;
  transition: all var(--motion-duration) var(--motion-ease) !important;
}

.btn-close:hover {
  background: rgba(107, 191, 75, 0.4) !important;
  opacity: 1 !important;
  transform: scale(1.1) !important;
}

/* Additional Performance Optimizations */
/* Reduce repaints and reflows */
.app-card,
.card,
.glass-panel {
  isolation: isolate;
  contain: layout style paint;
}

/* Optimize backdrop-filter performance */
@supports (backdrop-filter: blur(1px)) {
  .dropdown-menu,
  .navbar,
  .glass-panel,
  .card,
  .app-card {
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
  }
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(1px)) {
  .dropdown-menu,
  .navbar,
  .glass-panel,
  .card,
  .app-card {
    background: rgba(30, 30, 30, 0.9) !important;
  }
}

/* Optimize text rendering */
body,
.card-title,
.card-text,
h1, h2, h3, h4, h5, h6 {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reduce animation complexity on lower-end devices */
@media (max-width: 768px) and (prefers-reduced-motion: no-preference) {
  .app-card:hover,
  .card:hover {
    transform: translateY(-4px) !important;
  }

  .btn-primary:hover {
    transform: translateY(-2px) !important;
  }
}
