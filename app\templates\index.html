{% extends "base.html" %}

{% block title %}PEPE Store - Top #1 Tools for PC{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Hero Section -->
    <div class="hero-section text-white rounded-3 p-5 mb-5">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <span class="pepe-logo" style="font-size: 3rem; margin-right: 1rem;">🐸</span>
                    PEPE Store
                </h1>
                <h2 class="h3 mb-3">Top #1 Tools for PC</h2>
                <p class="lead">Discover the most powerful tools and applications for your PC. Join the PEPE community and unlock premium software.</p>
                <div class="mt-4 d-flex flex-wrap gap-3">
                    <span class="badge px-3 py-2" style="font-size: 0.9rem;">
                        <i class="bi bi-download me-2"></i> 1M+ Downloads
                    </span>
                    <span class="badge px-3 py-2" style="font-size: 0.9rem;">
                        <i class="bi bi-people me-2"></i> 500K+ Users
                    </span>
                    <span class="badge px-3 py-2" style="font-size: 0.9rem;">
                        <i class="bi bi-star-fill me-2"></i> #1 Rated
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <div class="display-1">🐸</div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <form method="GET" class="d-flex">
                <input type="text" name="search" class="form-control me-2" placeholder="Search apps..." value="{{ search_term }}">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i>
                </button>
            </form>
        </div>
        <div class="col-lg-4">
            <select class="form-select" onchange="filterByCategory(this.value)">
                <option value="">All Categories</option>
                {% for category in categories %}
                <option value="{{ category }}" {% if current_category == category %}selected{% endif %}>
                    {{ category }}
                </option>
                {% endfor %}
            </select>
        </div>
    </div>

    <!-- Featured Apps -->
    {% if featured_apps %}
    <section class="mb-5">
        <h2 class="mb-3">
            <i class="bi bi-star-fill text-warning"></i> Featured Apps
        </h2>
        <div class="row">
            {% for app in featured_apps %}
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card h-100 app-card">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-warning text-dark">
                            <i class="bi bi-star-fill"></i> FEATURED
                        </span>
                    </div>
                    <div class="card-img-top-container">
                        {% if app.icon_path %}
                        <img src="{{ url_for('main.uploaded_file', filename=app.icon_path) }}"
                             class="card-img-top app-icon" alt="{{ app.name }}">
                        {% else %}
                        <div class="card-img-top app-icon-placeholder">
                            <i class="bi bi-app"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ app.name }}</h6>
                        <p class="card-text text-muted small">by {{ app.developer }}</p>
                        <p class="card-text text-muted small">
                            <i class="bi bi-upload"></i> {{ app.uploaded_by }}
                        </p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="fw-bold text-success">
                                    {% if app.price == 0 %}FREE{% else %}&dollar;{{ "%.2f"|format(app.price) }}{% endif %}
                                </small>
                                <a href="{{ url_for('main.app_detail', app_id=app.id) }}" class="btn btn-sm btn-primary">View</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- All Apps -->
    <section>
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>
                {% if search_term %}
                    Search Results for "{{ search_term }}"
                {% elif current_category %}
                    {{ current_category }} Apps
                {% else %}
                    All Apps
                {% endif %}
            </h2>
            <span class="text-muted">{{ apps.total }} apps found</span>
        </div>

        {% if apps.items %}
        <div class="row">
            {% for app in apps.items %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 app-card">
                    {% if app.is_featured %}
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge bg-warning text-dark">
                            <i class="bi bi-star-fill"></i> FEATURED
                        </span>
                    </div>
                    {% endif %}
                    <div class="card-img-top-container">
                        {% if app.icon_path %}
                        <img src="{{ url_for('main.uploaded_file', filename=app.icon_path) }}"
                             class="card-img-top app-icon" alt="{{ app.name }}">
                        {% else %}
                        <div class="card-img-top app-icon-placeholder">
                            <i class="bi bi-app"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ app.name }}</h5>
                        <p class="card-text">{{ app.short_description }}</p>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-person"></i> {{ app.developer }}
                            </small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-upload"></i> Uploaded by {{ app.uploaded_by }}
                            </small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-download"></i> {{ app.downloads }} downloads
                            </small>
                        </div>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-secondary">{{ app.category }}</span>
                                <span class="fw-bold text-success">
                                    {% if app.price == 0 %}FREE{% else %}&dollar;{{ "%.2f"|format(app.price) }}{% endif %}
                                </span>
                            </div>
                            <div class="mt-2">
                                <a href="{{ url_for('main.app_detail', app_id=app.id) }}" class="btn btn-primary w-100">
                                    <i class="bi bi-eye"></i> View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if apps.pages > 1 %}
        <nav aria-label="Apps pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if apps.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.index', page=apps.prev_num, category=current_category, search=search_term) }}">
                        Previous
                    </a>
                </li>
                {% endif %}

                {% for page_num in apps.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != apps.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.index', page=page_num, category=current_category, search=search_term) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if apps.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.index', page=apps.next_num, category=current_category, search=search_term) }}">
                        Next
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-search display-1 text-muted"></i>
            <h3 class="mt-3">No apps found</h3>
            <p class="text-muted">Try adjusting your search criteria or browse all categories.</p>
            <a href="{{ url_for('main.index') }}" class="btn btn-primary">Browse All Apps</a>
        </div>
        {% endif %}
    </section>
</div>

<script>
function filterByCategory(category) {
    const url = new URL(window.location);
    if (category) {
        url.searchParams.set('category', category);
    } else {
        url.searchParams.delete('category');
    }
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}
</script>
{% endblock %}
